# Mobile Responsive Design Mockups

## 1. Mobile Dashboard (Staff View)

### Mobile Phone Layout (Portrait)
```
┌─────────────────────────────┐
│ ☰ StrongFit    🔔3  [👤▼]  │
├─────────────────────────────┤
│                             │
│        📊 DASHBOARD         │
│                             │
│ ┌─────────────────────────┐ │
│ │      TODAY'S STATS      │ │
│ │                         │ │
│ │  👥 47 Visitors         │ │
│ │  💰 Rp 850,000          │ │
│ │  🏠 12/30 Occupancy     │ │
│ │  ⚠️ 8 Expiring          │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │     QUICK ACTIONS       │ │
│ │                         │ │
│ │ ┌─────────────────────┐ │ │
│ │ │   📱 QUICK CHECK-IN │ │ │
│ │ └─────────────────────┘ │ │
│ │                         │ │
│ │ ┌─────────────────────┐ │ │
│ │ │  👤 ADD NEW MEMBER  │ │ │
│ │ └─────────────────────┘ │ │
│ │                         │ │
│ │ ┌─────────────────────┐ │ │
│ │ │  💰 RECORD PAYMENT  │ │ │
│ │ └─────────────────────┘ │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │   RECENT ACTIVITIES     │ │
│ │                         │ │
│ │ 🔵 John Doe - 2 min     │ │
│ │ 💰 Sarah - 5 min        │ │
│ │ 👤 Mike - 15 min        │ │
│ │ 🔵 Lisa - 23 min        │ │
│ │                         │ │
│ │ [View All]              │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │ 📊 📱 💰 👥 ⚙️         │ │
│ └─────────────────────────┘ │
└─────────────────────────────┘
```

### Key Mobile Features:
- Collapsible hamburger menu
- Stacked card layout for stats
- Large touch-friendly buttons (min 48px)
- Bottom navigation bar
- Condensed activity feed
- Swipe gestures for navigation

## 2. Mobile Member Search & Check-in

### Mobile Phone Layout
```
┌─────────────────────────────┐
│ ← StrongFit     🔔3  [👤▼] │
├─────────────────────────────┤
│                             │
│      📱 QUICK CHECK-IN      │
│                             │
│ ┌─────────────────────────┐ │
│ │ 🔍 Search member...     │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │    📷 SCAN QR CODE      │ │
│ └─────────────────────────┘ │
│                             │
│ Search Results:             │
│                             │
│ ┌─────────────────────────┐ │
│ │ 📷 GYM001               │ │
│ │ John Doe                │ │
│ │ +62 812-3456-7890       │ │
│ │ 🟢 Active • 29 days     │ │
│ │                         │ │
│ │ ┌─────────────────────┐ │ │
│ │ │   📱 CHECK-IN NOW   │ │ │
│ │ └─────────────────────┘ │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │ 📷 GYM045               │ │
│ │ Sarah Wilson            │ │
│ │ +62 821-9876-5432       │ │
│ │ 🟡 Expires in 5 days    │ │
│ │                         │ │
│ │ ┌─────────────────────┐ │ │
│ │ │   📱 CHECK-IN NOW   │ │ │
│ │ └─────────────────────┘ │ │
│ └─────────────────────────┘ │
│                             │
│ Current: 12/30 (40% full)   │
│                             │
└─────────────────────────────┘
```

### Key Features:
- Large search input with autocomplete
- QR scanner button prominently placed
- Member cards optimized for mobile
- One-tap check-in buttons
- Real-time occupancy at bottom
- Back navigation clearly visible

## 3. Mobile QR Code Scanner

### Full-Screen Scanner Interface
```
┌─────────────────────────────┐
│ ✕                    💡 🔄  │
├─────────────────────────────┤
│                             │
│                             │
│    ┌─────────────────┐      │
│    │                 │      │
│    │                 │      │
│    │   📷 CAMERA     │      │
│    │     VIEW        │      │
│    │                 │      │
│    │  ┌───────────┐  │      │
│    │  │  TARGET   │  │      │
│    │  │  SQUARE   │  │      │
│    │  └───────────┘  │      │
│    │                 │      │
│    │                 │      │
│    └─────────────────┘      │
│                             │
│                             │
│   Align QR code within      │
│      the target area        │
│                             │
│ ┌─────────────────────────┐ │
│ │    📝 MANUAL SEARCH     │ │
│ └─────────────────────────┘ │
│                             │
│ Occupancy: 12/30 (40%)      │
│                             │
└─────────────────────────────┘
```

### Key Features:
- Full-screen camera view
- Clear target square for QR alignment
- Flashlight toggle for low light
- Camera flip option
- Manual search fallback
- Occupancy indicator always visible
- Close button (✕) to exit scanner

## 4. Mobile Payment Form

### Mobile Payment Entry (Stacked Layout)
```
┌─────────────────────────────┐
│ ← Payment      💾    [👤▼]  │
├─────────────────────────────┤
│                             │
│     💰 RECORD PAYMENT       │
│                             │
│ Member                      │
│ ┌─────────────────────────┐ │
│ │ John Doe (GYM001) ▼     │ │
│ └─────────────────────────┘ │
│                             │
│ Package                     │
│ ┌─────────────────────────┐ │
│ │ Monthly Basic ▼         │ │
│ └─────────────────────────┘ │
│                             │
│ Amount                      │
│ ┌─────────────────────────┐ │
│ │ Rp 400,000              │ │
│ └─────────────────────────┘ │
│                             │
│ Payment Method              │
│ ┌─────────────────────────┐ │
│ │ Transfer ▼              │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │ Current: 2024-02-14     │ │
│ │ New: 2024-03-15         │ │
│ │ Extension: +30 days     │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │   📷 UPLOAD PROOF       │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │     💾 SAVE PAYMENT     │ │
│ └─────────────────────────┘ │
│                             │
└─────────────────────────────┘
```

### Key Features:
- Single column stacked layout
- Large dropdown selectors
- Clear expiry date calculation
- Photo upload with camera access
- Save button always visible
- Form validation with error states

## 5. Tablet Layout (iPad/Android Tablet)

### Tablet Dashboard (Landscape)
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ ☰ StrongFit Gym        [🔍 Search...]     [Quick Check-in]  [👤 Staff ▼] [🔔3] │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐               │
│ │  TODAY'S    │ │  TODAY'S    │ │  CURRENT    │ │  EXPIRING   │               │
│ │  VISITORS   │ │  REVENUE    │ │ OCCUPANCY   │ │ THIS WEEK   │               │
│ │             │ │             │ │             │ │             │               │
│ │     47      │ │ Rp 850,000  │ │   12/30     │ │      8      │               │
│ │  visitors   │ │   earned    │ │  members    │ │   members   │               │
│ │ ↗ +12%      │ │ ↗ +8%       │ │ 🟡 Moderate │ │ ⚠️ Action   │               │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘               │
│                                                                                 │
│ ┌─────────────────────────────────────┐ ┌─────────────────────────────────────┐ │
│ │          QUICK ACTIONS              │ │        RECENT ACTIVITIES            │ │
│ │                                     │ │                                     │ │
│ │ ┌─────────────────────────────────┐ │ │ 🔵 John Doe checked in - 2 min ago │ │
│ │ │        📱 QUICK CHECK-IN        │ │ │ 💰 Payment from Sarah - 5 min ago  │ │
│ │ └─────────────────────────────────┘ │ │ 👤 New member: Mike - 15 min ago   │ │
│ │                                     │ │ 🔵 Lisa Wong checked in - 23 min   │ │
│ │ ┌─────────────────────────────────┐ │ │                                     │ │
│ │ │        👤 ADD NEW MEMBER        │ │ │ [View All Activities]               │ │
│ │ └─────────────────────────────────┘ │ │                                     │ │
│ │                                     │ │                                     │ │
│ │ ┌─────────────────────────────────┐ │ │                                     │ │
│ │ │        💰 RECORD PAYMENT        │ │ │                                     │ │
│ │ └─────────────────────────────────┘ │ │                                     │ │
│ └─────────────────────────────────────┘ └─────────────────────────────────────┘ │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### Key Tablet Features:
- Two-column layout for better space utilization
- Larger touch targets (48px minimum)
- Side-by-side cards for quick actions and activities
- Persistent search bar and quick actions in header
- Optimized for landscape orientation
- Easy one-handed operation when held vertically

## 6. Progressive Web App (PWA) Features

### Offline Mode Indicator
```
┌─────────────────────────────┐
│ ⚠️ OFFLINE MODE             │
├─────────────────────────────┤
│                             │
│ Limited functionality       │
│ available:                  │
│                             │
│ ✅ View member list         │
│ ✅ Search members           │
│ ✅ View member profiles     │
│ ❌ Record payments          │
│ ❌ Check-in members         │
│ ❌ Add new members          │
│                             │
│ Data will sync when         │
│ connection is restored      │
│                             │
│ ┌─────────────────────────┐ │
│ │    🔄 RETRY CONNECTION  │ │
│ └─────────────────────────┘ │
│                             │
└─────────────────────────────┘
```

### PWA Install Prompt
```
┌─────────────────────────────┐
│ 📱 Install StrongFit Gym    │
├─────────────────────────────┤
│                             │
│ Add to your home screen     │
│ for quick access:           │
│                             │
│ • Faster loading            │
│ • Offline access            │
│ • Native app experience     │
│ • Push notifications        │
│                             │
│ ┌─────────────────────────┐ │
│ │      📱 INSTALL         │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │      ❌ NOT NOW         │ │
│ └─────────────────────────┘ │
│                             │
└─────────────────────────────┘
```

### Key PWA Features:
- Offline capability for basic functions
- App-like experience with home screen icon
- Push notifications for important alerts
- Background sync when connection restored
- Fast loading with service worker caching
- Native-like navigation and gestures

## 7. Responsive Breakpoints

### Design System Breakpoints:
```
Mobile (Portrait):  320px - 480px
Mobile (Landscape): 481px - 768px
Tablet (Portrait):  769px - 1024px
Tablet (Landscape): 1025px - 1200px
Desktop:           1201px+
```

### Key Responsive Features:
- **Mobile-first design approach**
- **Touch-optimized interface** (48px minimum touch targets)
- **Swipe gestures** for navigation and actions
- **Collapsible navigation** for space efficiency
- **Stacked layouts** on smaller screens
- **Progressive enhancement** for larger screens
- **Adaptive typography** scaling with screen size
- **Flexible grid system** for different orientations
