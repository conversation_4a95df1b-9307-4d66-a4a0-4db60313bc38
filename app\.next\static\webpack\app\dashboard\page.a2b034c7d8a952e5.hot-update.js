"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/layout/main-layout.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/main-layout.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MainLayout: () => (/* binding */ MainLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./header */ \"(app-pages-browser)/./src/components/layout/header.tsx\");\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sidebar */ \"(app-pages-browser)/./src/components/layout/sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ MainLayout auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction MainLayout(param) {\n    let { children, user } = param;\n    _s();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleSidebar = ()=>setSidebarOpen(!sidebarOpen);\n    const closeSidebar = ()=>setSidebarOpen(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                isOpen: sidebarOpen,\n                onClose: closeSidebar,\n                userRole: user === null || user === void 0 ? void 0 : user.role\n            }, void 0, false, {\n                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header__WEBPACK_IMPORTED_MODULE_2__.Header, {\n                        onMenuToggle: toggleSidebar,\n                        user: user\n                    }, void 0, false, {\n                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-auto p-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\strongfitgym.id\\\\app\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n_s(MainLayout, \"5rGDkYpGQ8fHM9RkMWnKOwsxadk=\");\n_c = MainLayout;\nvar _c;\n$RefreshReg$(_c, \"MainLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/main-layout.tsx\n"));

/***/ })

});