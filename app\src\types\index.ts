export interface User {
  id: string
  email: string
  role: 'owner' | 'staff' | 'member'
  displayName: string
  photoURL?: string
  createdAt: Date
  lastLogin: Date
}

export interface Member {
  id: string
  memberId: string // GYM001, GYM002, etc.
  fullName: string
  phoneNumber: string
  email?: string
  address?: string
  emergencyContact?: string
  photoURL?: string
  packageType: PackageType
  startDate: Date
  expiryDate: Date
  status: 'active' | 'expires-soon' | 'expired'
  totalVisits: number
  lastVisit?: Date
  createdAt: Date
  createdBy: string
}

export interface Package {
  id: string
  name: string
  price: number
  duration: number // days
  description: string
  features: string[]
}

export type PackageType = 'daily' | 'weekly' | 'monthly-basic' | 'monthly-unlimited' | 'quarterly'

export interface Payment {
  id: string
  memberId: string
  memberName: string
  amount: number
  method: 'cash' | 'transfer' | 'qris' | 'ewallet'
  packageType: PackageType
  duration: number
  proofURL?: string
  processedBy: string
  paymentDate: Date
  receiptNumber: string
  notes?: string
}

export interface Visit {
  id: string
  memberId: string
  memberName: string
  checkInTime: Date
  processedBy: string
  visitType: 'gym' | 'class'
  notes?: string
}

export interface StudioSettings {
  name: string
  address: string
  phone: string
  email: string
  maxCapacity: number
  operatingHours: {
    open: string
    close: string
  }
  packages: Record<PackageType, Package>
}

export interface DashboardStats {
  todayVisitors: number
  todayRevenue: number
  currentOccupancy: number
  maxCapacity: number
  expiringThisWeek: number
  activeMembers: number
  visitorsTrend: number
  revenueTrend: number
}

export interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
}

export interface AppState {
  auth: AuthState
  members: Member[]
  payments: Payment[]
  visits: Visit[]
  settings: StudioSettings
  isLoading: boolean
}
