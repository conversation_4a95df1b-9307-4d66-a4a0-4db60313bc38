'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Save, CreditCard, X, Camera, Upload } from 'lucide-react'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { LoadingSpinner } from '@/components/ui/loading'
import { useAuth } from '@/hooks/useAuth'
import { mockMembers, mockPackages } from '@/data/mockData'
import { PackageType } from '@/types'
import { generateMemberId, calculateExpiryDate, formatDate, formatCurrency } from '@/lib/utils'

interface MemberFormData {
  fullName: string
  phoneNumber: string
  email: string
  address: string
  emergencyContact: string
  packageType: PackageType
  startDate: string
  photoFile: File | null
}

export default function NewMemberPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState<MemberFormData>({
    fullName: '',
    phoneNumber: '',
    email: '',
    address: '',
    emergencyContact: '',
    packageType: 'monthly-basic',
    startDate: new Date().toISOString().split('T')[0],
    photoFile: null,
  })

  const newMemberId = generateMemberId(mockMembers.length)
  const startDate = new Date(formData.startDate)
  const expiryDate = calculateExpiryDate(startDate, formData.packageType)
  const selectedPackage = mockPackages[formData.packageType]

  const handleInputChange = (field: keyof MemberFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null
    setFormData(prev => ({ ...prev, photoFile: file }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      console.log('Creating new member:', {
        ...formData,
        memberId: newMemberId,
        startDate,
        expiryDate,
      })

      // TODO: Actually save the member data
      router.push('/members')
    } catch (error) {
      console.error('Error creating member:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveAndAddPayment = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      console.log('Creating new member and redirecting to payment:', {
        ...formData,
        memberId: newMemberId,
        startDate,
        expiryDate,
      })

      // TODO: Actually save the member data and redirect to payment
      router.push('/payments/new')
    } catch (error) {
      console.error('Error creating member:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <ProtectedRoute>
      <MainLayout user={user}>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold">📝 New Member Registration</h1>
              <p className="text-muted-foreground">Complete member information in 3 minutes</p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-3">
              {/* Personal Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Full Name *</label>
                    <Input
                      value={formData.fullName}
                      onChange={(e) => handleInputChange('fullName', e.target.value)}
                      placeholder="John Doe"
                      required
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Phone Number *</label>
                    <Input
                      value={formData.phoneNumber}
                      onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                      placeholder="+62 812-3456-7890"
                      required
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Email Address</label>
                    <Input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Address</label>
                    <Input
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      placeholder="Jl. Sudirman No. 123, Jakarta"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Emergency Contact</label>
                    <Input
                      value={formData.emergencyContact}
                      onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
                      placeholder="Jane Doe - +62 812-9876-5432"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Membership Package */}
              <Card>
                <CardHeader>
                  <CardTitle>Membership Package</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Select Package *</label>
                    <Select
                      value={formData.packageType}
                      onChange={(e) => handleInputChange('packageType', e.target.value as PackageType)}
                      required
                    >
                      {Object.entries(mockPackages).map(([key, pkg]) => (
                        <option key={key} value={key}>
                          {pkg.name} - {formatCurrency(pkg.price)}
                        </option>
                      ))}
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Start Date</label>
                    <Input
                      type="date"
                      value={formData.startDate}
                      onChange={(e) => handleInputChange('startDate', e.target.value)}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Expiry Date (Auto-calculated)</label>
                    <Input
                      value={formatDate(expiryDate)}
                      disabled
                      className="bg-muted"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Member ID (Auto-generated)</label>
                    <Input
                      value={newMemberId}
                      disabled
                      className="bg-muted"
                    />
                  </div>

                  {/* Package Details */}
                  <div className="p-3 bg-muted rounded-md">
                    <p className="font-medium text-sm mb-2">Package Details:</p>
                    <ul className="text-xs space-y-1">
                      {selectedPackage.features.map((feature, index) => (
                        <li key={index}>• {feature}</li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>

              {/* Profile Photo & Actions */}
              <div className="space-y-6">
                {/* Profile Photo */}
                <Card>
                  <CardHeader>
                    <CardTitle>Profile Photo</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                        {formData.photoFile ? (
                          <div className="space-y-2">
                            <div className="text-sm font-medium">
                              {formData.photoFile.name}
                            </div>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => setFormData(prev => ({ ...prev, photoFile: null }))}
                            >
                              <X className="h-3 w-3 mr-1" />
                              Remove
                            </Button>
                          </div>
                        ) : (
                          <div className="space-y-2">
                            <div className="text-4xl">📷</div>
                            <div className="text-sm font-medium">Upload Photo</div>
                            <div className="text-xs text-muted-foreground">(Optional)</div>
                          </div>
                        )}
                      </div>

                      <div className="flex space-x-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="flex-1"
                          onClick={() => document.getElementById('photo-upload')?.click()}
                        >
                          <Upload className="h-3 w-3 mr-1" />
                          Choose File
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="flex-1"
                        >
                          <Camera className="h-3 w-3 mr-1" />
                          Camera
                        </Button>
                      </div>

                      <input
                        id="photo-upload"
                        type="file"
                        accept="image/*"
                        onChange={handleFileChange}
                        className="hidden"
                      />

                      <div className="text-xs text-muted-foreground">
                        Max size: 5MB<br />
                        Formats: JPG, PNG
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Actions */}
                <Card>
                  <CardHeader>
                    <CardTitle>Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button
                      type="submit"
                      className="w-full"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <LoadingSpinner className="mr-2" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          Save Member
                        </>
                      )}
                    </Button>

                    <Button
                      type="button"
                      variant="outline"
                      className="w-full"
                      onClick={handleSaveAndAddPayment}
                      disabled={isLoading}
                    >
                      <Save className="h-4 w-4 mr-2" />
                      <CreditCard className="h-4 w-4 mr-2" />
                      Save & Add Payment
                    </Button>

                    <Button
                      type="button"
                      variant="outline"
                      className="w-full"
                      onClick={() => router.back()}
                      disabled={isLoading}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </form>
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
