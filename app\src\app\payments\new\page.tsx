'use client'

import { useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { ArrowLeft } from 'lucide-react'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { MainLayout } from '@/components/layout/main-layout'
import { PaymentForm } from '@/components/features/payments/payment-form'
import { Receipt } from '@/components/features/payments/receipt'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import { mockMembers, mockPackages } from '@/data/mockData'
import { Member, Payment } from '@/types'
import { calculateExpiryDate } from '@/lib/utils'

type PaymentMode = 'form' | 'receipt'

export default function NewPaymentPage() {
  const { user } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [mode, setMode] = useState<PaymentMode>('form')
  const [isLoading, setIsLoading] = useState(false)
  const [processedPayment, setProcessedPayment] = useState<Payment | null>(null)
  const [newExpiryDate, setNewExpiryDate] = useState<Date>(new Date())

  // Get member from URL params if provided
  const memberId = searchParams.get('memberId')
  const selectedMember = memberId ? mockMembers.find(m => m.id === memberId) : undefined

  const handlePaymentSubmit = async (formData: any) => {
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Generate receipt number
      const receiptNumber = `RCP${Date.now().toString().slice(-6)}`
      
      // Calculate new expiry date
      const member = mockMembers.find(m => m.memberId === formData.memberId)
      const startDate = member?.expiryDate && member.expiryDate > new Date() 
        ? member.expiryDate 
        : new Date()
      const expiryDate = calculateExpiryDate(startDate, formData.packageType)

      // Create payment record
      const payment: Payment = {
        id: `payment-${Date.now()}`,
        memberId: formData.memberId,
        memberName: formData.memberName,
        amount: formData.amount,
        method: formData.method,
        packageType: formData.packageType,
        duration: mockPackages[formData.packageType].duration,
        processedBy: user?.id || 'user-1',
        paymentDate: new Date(),
        receiptNumber,
        notes: formData.notes,
      }

      console.log('Payment processed:', payment)
      console.log('New expiry date:', expiryDate)

      setProcessedPayment(payment)
      setNewExpiryDate(expiryDate)
      setMode('receipt')

      // TODO: Actually save the payment and update member expiry date
    } catch (error) {
      console.error('Error processing payment:', error)
      alert('Error processing payment. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleNewPayment = () => {
    setProcessedPayment(null)
    setMode('form')
  }

  const handleBackToPayments = () => {
    router.push('/payments')
  }

  const handleCancel = () => {
    router.back()
  }

  if (mode === 'receipt' && processedPayment) {
    return (
      <ProtectedRoute>
        <MainLayout user={user}>
          <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="icon"
                onClick={() => router.push('/payments')}
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-3xl font-bold">💰 Payment Receipt</h1>
                <p className="text-muted-foreground">Payment processed successfully</p>
              </div>
            </div>

            <Receipt
              payment={processedPayment}
              memberName={processedPayment.memberName}
              newExpiryDate={newExpiryDate}
              onNewPayment={handleNewPayment}
              onBackToPayments={handleBackToPayments}
            />
          </div>
        </MainLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <MainLayout user={user}>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="icon"
              onClick={handleCancel}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold">💰 Record Payment</h1>
              <p className="text-muted-foreground">
                {selectedMember 
                  ? `Processing payment for ${selectedMember.fullName}` 
                  : 'Complete payment in 2 minutes'
                }
              </p>
            </div>
          </div>

          <PaymentForm
            selectedMember={selectedMember}
            onSubmit={handlePaymentSubmit}
            onCancel={handleCancel}
            isLoading={isLoading}
          />
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
