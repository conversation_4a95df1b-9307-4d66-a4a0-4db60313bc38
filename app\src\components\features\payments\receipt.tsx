'use client'

import { useRef } from 'react'
import { Download, Printer, Share, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Payment } from '@/types'
import { formatCurrency, formatDateTime, formatDate } from '@/lib/utils'

interface ReceiptProps {
  payment: Payment
  memberName: string
  newExpiryDate: Date
  onNewPayment: () => void
  onBackToPayments: () => void
}

export function Receipt({ 
  payment, 
  memberName, 
  newExpiryDate, 
  onNewPayment, 
  onBackToPayments 
}: ReceiptProps) {
  const receiptRef = useRef<HTMLDivElement>(null)

  const handlePrint = () => {
    if (receiptRef.current) {
      const printContent = receiptRef.current.innerHTML
      const printWindow = window.open('', '_blank')
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>Receipt - ${payment.receiptNumber}</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .receipt { max-width: 400px; margin: 0 auto; }
                .header { text-align: center; margin-bottom: 20px; }
                .divider { border-top: 1px dashed #ccc; margin: 15px 0; }
                .row { display: flex; justify-content: space-between; margin: 5px 0; }
                .total { font-weight: bold; font-size: 1.1em; }
                @media print { body { margin: 0; } }
              </style>
            </head>
            <body>
              <div class="receipt">${printContent}</div>
            </body>
          </html>
        `)
        printWindow.document.close()
        printWindow.print()
      }
    }
  }

  const handleDownload = () => {
    // In a real app, this would generate a PDF
    alert('PDF download functionality would be implemented here')
  }

  const handleShare = () => {
    // In a real app, this would share via WhatsApp or other methods
    alert('Share functionality would be implemented here (WhatsApp, Email, etc.)')
  }

  const getPaymentMethodDisplay = (method: string) => {
    switch (method) {
      case 'cash':
        return '💰 Cash'
      case 'transfer':
        return '🏦 Bank Transfer'
      case 'qris':
        return '📱 QRIS'
      case 'ewallet':
        return '💳 E-wallet'
      default:
        return method
    }
  }

  const getPackageDisplayName = (packageType: string) => {
    switch (packageType) {
      case 'daily':
        return 'Daily Pass'
      case 'weekly':
        return 'Weekly Pass'
      case 'monthly-basic':
        return 'Monthly Basic'
      case 'monthly-unlimited':
        return 'Monthly Unlimited'
      case 'quarterly':
        return 'Quarterly'
      default:
        return packageType
    }
  }

  return (
    <div className="space-y-6">
      {/* Success Header */}
      <Card className="border-green-200 bg-green-50">
        <CardContent className="p-6 text-center">
          <div className="space-y-4">
            <div className="flex justify-center">
              <CheckCircle className="h-16 w-16 text-green-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-green-800 mb-2">
                ✅ Payment Successful!
              </h2>
              <p className="text-green-700">
                Receipt generated successfully
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Receipt */}
      <Card>
        <CardContent className="p-0">
          <div ref={receiptRef} className="p-6 max-w-md mx-auto">
            {/* Header */}
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-primary mb-2">StrongFit Gym</h1>
              <p className="text-sm text-muted-foreground">Management System</p>
              <p className="text-xs text-muted-foreground mt-1">
                Jl. Sudirman No. 123, Jakarta Selatan<br />
                Phone: +62 21 1234 5678
              </p>
            </div>

            {/* Receipt Details */}
            <div className="space-y-3 text-sm">
              <div className="text-center">
                <h2 className="font-bold text-lg">PAYMENT RECEIPT</h2>
                <p className="text-muted-foreground">#{payment.receiptNumber}</p>
              </div>

              <div className="border-t border-dashed border-gray-300 pt-3">
                <div className="flex justify-between">
                  <span>Date & Time:</span>
                  <span>{formatDateTime(payment.paymentDate)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Member ID:</span>
                  <span>{payment.memberId}</span>
                </div>
                <div className="flex justify-between">
                  <span>Member Name:</span>
                  <span>{memberName}</span>
                </div>
              </div>

              <div className="border-t border-dashed border-gray-300 pt-3">
                <div className="flex justify-between">
                  <span>Package:</span>
                  <span>{getPackageDisplayName(payment.packageType)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Duration:</span>
                  <span>{payment.duration} days</span>
                </div>
                <div className="flex justify-between">
                  <span>New Expiry:</span>
                  <span>{formatDate(newExpiryDate)}</span>
                </div>
              </div>

              <div className="border-t border-dashed border-gray-300 pt-3">
                <div className="flex justify-between">
                  <span>Payment Method:</span>
                  <span>{getPaymentMethodDisplay(payment.method)}</span>
                </div>
                {payment.notes && (
                  <div className="flex justify-between">
                    <span>Notes:</span>
                    <span className="text-right max-w-32 break-words">{payment.notes}</span>
                  </div>
                )}
              </div>

              <div className="border-t border-dashed border-gray-300 pt-3">
                <div className="flex justify-between text-lg font-bold">
                  <span>TOTAL PAID:</span>
                  <span>{formatCurrency(payment.amount)}</span>
                </div>
              </div>
            </div>

            {/* QR Code Placeholder */}
            <div className="mt-6 text-center">
              <div className="inline-block p-4 border-2 border-dashed border-gray-300 rounded">
                <div className="w-24 h-24 bg-gray-100 flex items-center justify-center text-xs text-gray-500">
                  QR CODE<br />
                  {payment.receiptNumber}
                </div>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Scan for digital receipt verification
              </p>
            </div>

            {/* Footer */}
            <div className="mt-6 text-center text-xs text-muted-foreground">
              <p>Thank you for choosing StrongFit Gym!</p>
              <p>Keep this receipt for your records</p>
              <p className="mt-2">
                Generated on {formatDateTime(new Date())}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Button onClick={handlePrint} variant="outline" className="w-full">
            <Printer className="h-4 w-4 mr-2" />
            Print Receipt
          </Button>
          <Button onClick={handleDownload} variant="outline" className="w-full">
            <Download className="h-4 w-4 mr-2" />
            Download PDF
          </Button>
        </div>
        
        <div className="space-y-2">
          <Button onClick={handleShare} variant="outline" className="w-full">
            <Share className="h-4 w-4 mr-2" />
            Share Receipt
          </Button>
          <Button onClick={onNewPayment} className="w-full">
            💰 New Payment
          </Button>
        </div>
      </div>

      <div className="text-center">
        <Button variant="ghost" onClick={onBackToPayments}>
          ← Back to Payments
        </Button>
      </div>
    </div>
  )
}
