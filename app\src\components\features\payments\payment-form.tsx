'use client'

import { useState } from 'react'
import { CreditCard, Upload, X, Calculator } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { LoadingSpinner } from '@/components/ui/loading'
import { Member, PackageType } from '@/types'
import { mockPackages } from '@/data/mockData'
import { formatCurrency, calculateExpiryDate, formatDate } from '@/lib/utils'

interface PaymentFormData {
  memberId: string
  memberName: string
  packageType: PackageType
  amount: number
  method: 'cash' | 'transfer' | 'qris' | 'ewallet'
  proofFile: File | null
  notes: string
}

interface PaymentFormProps {
  selectedMember?: Member
  onSubmit: (data: PaymentFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function PaymentForm({ selectedMember, onSubmit, onCancel, isLoading = false }: PaymentFormProps) {
  const [formData, setFormData] = useState<PaymentFormData>({
    memberId: selectedMember?.memberId || '',
    memberName: selectedMember?.fullName || '',
    packageType: selectedMember?.packageType || 'monthly-basic',
    amount: selectedMember ? mockPackages[selectedMember.packageType].price : mockPackages['monthly-basic'].price,
    method: 'cash',
    proofFile: null,
    notes: '',
  })

  const selectedPackage = mockPackages[formData.packageType]
  const startDate = selectedMember?.expiryDate && selectedMember.expiryDate > new Date() 
    ? selectedMember.expiryDate 
    : new Date()
  const newExpiryDate = calculateExpiryDate(startDate, formData.packageType)

  const handleInputChange = (field: keyof PaymentFormData, value: any) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value }
      
      // Auto-update amount when package changes
      if (field === 'packageType') {
        updated.amount = mockPackages[value as PackageType].price
      }
      
      return updated
    })
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null
    setFormData(prev => ({ ...prev, proofFile: file }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    await onSubmit(formData)
  }

  const needsProof = formData.method !== 'cash'

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Member & Package Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Member & Package Selection</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium">Member ID *</label>
              <Input
                value={formData.memberId}
                onChange={(e) => handleInputChange('memberId', e.target.value)}
                placeholder="GYM001"
                required
                disabled={!!selectedMember}
              />
            </div>

            <div>
              <label className="text-sm font-medium">Member Name *</label>
              <Input
                value={formData.memberName}
                onChange={(e) => handleInputChange('memberName', e.target.value)}
                placeholder="John Doe"
                required
                disabled={!!selectedMember}
              />
            </div>

            <div>
              <label className="text-sm font-medium">Package Type *</label>
              <Select
                value={formData.packageType}
                onChange={(e) => handleInputChange('packageType', e.target.value as PackageType)}
                required
              >
                {Object.entries(mockPackages).map(([key, pkg]) => (
                  <option key={key} value={key}>
                    {pkg.name} - {formatCurrency(pkg.price)}
                  </option>
                ))}
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">Amount *</label>
              <div className="relative">
                <Input
                  type="number"
                  value={formData.amount}
                  onChange={(e) => handleInputChange('amount', parseInt(e.target.value) || 0)}
                  required
                  min="0"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6"
                  onClick={() => handleInputChange('amount', selectedPackage.price)}
                  title="Reset to package price"
                >
                  <Calculator className="h-3 w-3" />
                </Button>
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                Package price: {formatCurrency(selectedPackage.price)}
              </div>
            </div>

            {/* Package Details */}
            <div className="p-3 bg-muted rounded-md">
              <p className="font-medium text-sm mb-2">Package Details:</p>
              <ul className="text-xs space-y-1">
                <li>• Duration: {selectedPackage.duration} days</li>
                {selectedPackage.features.map((feature, index) => (
                  <li key={index}>• {feature}</li>
                ))}
              </ul>
              <div className="mt-2 text-xs">
                <p><strong>Current Expiry:</strong> {selectedMember ? formatDate(selectedMember.expiryDate) : 'N/A'}</p>
                <p><strong>New Expiry:</strong> {formatDate(newExpiryDate)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Details */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium">Payment Method *</label>
              <Select
                value={formData.method}
                onChange={(e) => handleInputChange('method', e.target.value)}
                required
              >
                <option value="cash">💰 Cash</option>
                <option value="transfer">🏦 Bank Transfer</option>
                <option value="qris">📱 QRIS</option>
                <option value="ewallet">💳 E-wallet</option>
              </Select>
            </div>

            {needsProof && (
              <div>
                <label className="text-sm font-medium">Payment Proof *</label>
                <div className="space-y-3">
                  <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 text-center">
                    {formData.proofFile ? (
                      <div className="space-y-2">
                        <div className="text-sm font-medium">
                          📄 {formData.proofFile.name}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {(formData.proofFile.size / 1024 / 1024).toFixed(2)} MB
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setFormData(prev => ({ ...prev, proofFile: null }))}
                        >
                          <X className="h-3 w-3 mr-1" />
                          Remove
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <div className="text-2xl">📷</div>
                        <div className="text-sm font-medium">Upload Payment Proof</div>
                        <div className="text-xs text-muted-foreground">
                          Screenshot or photo of payment confirmation
                        </div>
                      </div>
                    )}
                  </div>

                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => document.getElementById('proof-upload')?.click()}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    {formData.proofFile ? 'Change File' : 'Choose File'}
                  </Button>

                  <input
                    id="proof-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                    required={needsProof}
                  />

                  <div className="text-xs text-muted-foreground">
                    Max size: 10MB • Formats: JPG, PNG, PDF
                  </div>
                </div>
              </div>
            )}

            <div>
              <label className="text-sm font-medium">Notes (Optional)</label>
              <Input
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Additional notes..."
              />
            </div>

            {/* Payment Summary */}
            <div className="p-4 bg-primary/5 rounded-lg border border-primary/20">
              <h4 className="font-medium mb-3">Payment Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Package:</span>
                  <span>{selectedPackage.name}</span>
                </div>
                <div className="flex justify-between">
                  <span>Duration:</span>
                  <span>{selectedPackage.duration} days</span>
                </div>
                <div className="flex justify-between">
                  <span>Method:</span>
                  <span className="capitalize">{formData.method}</span>
                </div>
                <div className="border-t pt-2 flex justify-between font-medium">
                  <span>Total Amount:</span>
                  <span>{formatCurrency(formData.amount)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-4">
        <Button
          type="submit"
          className="flex-1"
          disabled={isLoading || (needsProof && !formData.proofFile)}
        >
          {isLoading ? (
            <>
              <LoadingSpinner className="mr-2" />
              Processing...
            </>
          ) : (
            <>
              <CreditCard className="h-4 w-4 mr-2" />
              Process Payment
            </>
          )}
        </Button>
        
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
      </div>
    </form>
  )
}
