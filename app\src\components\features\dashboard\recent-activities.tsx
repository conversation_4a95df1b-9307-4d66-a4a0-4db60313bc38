import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Smartphone, CreditCard, UserPlus } from 'lucide-react'
import { formatCurrency, formatDateTime } from '@/lib/utils'
import { Visit, Payment } from '@/types'

interface Activity {
  id: string
  type: 'checkin' | 'payment' | 'registration'
  memberName: string
  timestamp: Date
  details?: string
  amount?: number
}

interface RecentActivitiesProps {
  activities: Activity[]
  maxItems?: number
}

export function RecentActivities({ activities, maxItems = 5 }: RecentActivitiesProps) {
  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'checkin':
        return <Smartphone className="h-4 w-4 text-blue-600" />
      case 'payment':
        return <CreditCard className="h-4 w-4 text-green-600" />
      case 'registration':
        return <UserPlus className="h-4 w-4 text-purple-600" />
    }
  }

  const getActivityBadge = (type: Activity['type']) => {
    switch (type) {
      case 'checkin':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Check-in</Badge>
      case 'payment':
        return <Badge variant="success">Payment</Badge>
      case 'registration':
        return <Badge variant="secondary">New Member</Badge>
    }
  }

  const formatActivityText = (activity: Activity) => {
    const timeAgo = getTimeAgo(activity.timestamp)
    
    switch (activity.type) {
      case 'checkin':
        return `${activity.memberName} checked in - ${timeAgo}`
      case 'payment':
        return `Payment received from ${activity.memberName} - ${timeAgo}${activity.amount ? ` (${formatCurrency(activity.amount)})` : ''}`
      case 'registration':
        return `New member registered: ${activity.memberName} - ${timeAgo}`
    }
  }

  const getTimeAgo = (timestamp: Date) => {
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'just now'
    if (diffInMinutes < 60) return `${diffInMinutes} min ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
  }

  const displayedActivities = activities.slice(0, maxItems)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Recent Activities</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {displayedActivities.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-4">
              No recent activities
            </p>
          ) : (
            displayedActivities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-foreground">
                    {formatActivityText(activity)}
                  </p>
                  {activity.details && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {activity.details}
                    </p>
                  )}
                </div>
                <div className="flex-shrink-0">
                  {getActivityBadge(activity.type)}
                </div>
              </div>
            ))
          )}
        </div>
        
        {activities.length > maxItems && (
          <div className="mt-4 text-center">
            <Button variant="outline" size="sm">
              View All Activities
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Helper function to convert visits and payments to activities
export function createActivitiesFromData(
  visits: Visit[], 
  payments: Payment[]
): Activity[] {
  const activities: Activity[] = []

  // Add check-ins
  visits.forEach(visit => {
    activities.push({
      id: `visit-${visit.id}`,
      type: 'checkin',
      memberName: visit.memberName,
      timestamp: visit.checkInTime,
    })
  })

  // Add payments
  payments.forEach(payment => {
    activities.push({
      id: `payment-${payment.id}`,
      type: 'payment',
      memberName: payment.memberName,
      timestamp: payment.paymentDate,
      amount: payment.amount,
      details: payment.notes,
    })
  })

  // Sort by timestamp (most recent first)
  return activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
}
