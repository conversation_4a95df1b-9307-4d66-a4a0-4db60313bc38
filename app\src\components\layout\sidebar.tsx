'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  BarChart3, 
  Users, 
  Smartphone, 
  CreditCard, 
  TrendingUp, 
  Settings,
  ChevronDown,
  ChevronRight,
  UserPlus,
  UserX,
  Receipt,
  History
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
  userRole?: 'owner' | 'staff'
}

interface MenuItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  children?: MenuItem[]
  roles?: ('owner' | 'staff')[]
}

const menuItems: MenuItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: BarChart3,
  },
  {
    title: 'Members',
    href: '/members',
    icon: Users,
    children: [
      { title: 'All Members', href: '/members', icon: Users },
      { title: 'Add New Member', href: '/members/new', icon: UserPlus },
      { title: 'Expired Members', href: '/members/expired', icon: UserX },
    ],
  },
  {
    title: 'Check-in',
    href: '/checkin',
    icon: Smartphone,
  },
  {
    title: 'Payments',
    href: '/payments',
    icon: CreditCard,
    children: [
      { title: 'Record Payment', href: '/payments/new', icon: CreditCard },
      { title: 'Payment History', href: '/payments/history', icon: History },
    ],
  },
  {
    title: 'Reports',
    href: '/reports',
    icon: TrendingUp,
    roles: ['owner'],
    children: [
      { title: 'Daily Report', href: '/reports/daily', icon: Receipt },
      { title: 'Monthly Report', href: '/reports/monthly', icon: TrendingUp },
    ],
  },
  {
    title: 'Settings',
    href: '/settings',
    icon: Settings,
    roles: ['owner'],
  },
]

export function Sidebar({ isOpen, onClose, userRole = 'staff' }: SidebarProps) {
  const pathname = usePathname()
  const [expandedItems, setExpandedItems] = useState<string[]>(['Members', 'Payments'])

  const toggleExpanded = (title: string) => {
    setExpandedItems(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    )
  }

  const filteredMenuItems = menuItems.filter(item => 
    !item.roles || item.roles.includes(userRole)
  )

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 md:hidden" 
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside className={cn(
        "fixed left-0 top-0 z-50 h-full w-64 transform border-r bg-background transition-transform duration-200 ease-in-out md:relative md:translate-x-0",
        isOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="flex h-full flex-col">
          {/* Logo */}
          <div className="flex h-16 items-center border-b px-6">
            <div className="font-bold text-xl text-primary">StrongFit Gym</div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 space-y-1 p-4">
            {filteredMenuItems.map((item) => (
              <div key={item.title}>
                {item.children ? (
                  <div>
                    <Button
                      variant="ghost"
                      className={cn(
                        "w-full justify-between px-3 py-2 text-left font-normal",
                        pathname.startsWith(item.href) && "bg-accent text-accent-foreground"
                      )}
                      onClick={() => toggleExpanded(item.title)}
                    >
                      <div className="flex items-center">
                        <item.icon className="mr-3 h-4 w-4" />
                        {item.title}
                      </div>
                      {expandedItems.includes(item.title) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                    
                    {expandedItems.includes(item.title) && (
                      <div className="ml-6 mt-1 space-y-1">
                        {item.children.map((child) => (
                          <Link key={child.href} href={child.href}>
                            <Button
                              variant="ghost"
                              className={cn(
                                "w-full justify-start px-3 py-2 text-sm font-normal",
                                pathname === child.href && "bg-accent text-accent-foreground"
                              )}
                              onClick={onClose}
                            >
                              <child.icon className="mr-3 h-3 w-3" />
                              {child.title}
                            </Button>
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link href={item.href}>
                    <Button
                      variant="ghost"
                      className={cn(
                        "w-full justify-start px-3 py-2 font-normal",
                        pathname === item.href && "bg-accent text-accent-foreground"
                      )}
                      onClick={onClose}
                    >
                      <item.icon className="mr-3 h-4 w-4" />
                      {item.title}
                    </Button>
                  </Link>
                )}
              </div>
            ))}
          </nav>
        </div>
      </aside>
    </>
  )
}
