# MVP Requirements - Studio Gym Management System
## Firebase/Firestore Backend untuk Studio Gym Skala Awal

### 1. Project Overview

#### 1.1 Target Business
- **Studio Gym Kecil**: 50-200 members capacity
- **Lokasi**: 1 studio dengan space terbatas
- **Staff**: 1-3 orang (owner + 1-2 trainer/front desk)
- **Equipment**: Basic gym equipment + group class area
- **Timeline**: 2-3 bulan development
- **Budget**: Rp 100-200 juta

#### 1.2 Business Goals
- Digitalisasi member management dari Excel/manual
- Track revenue dan member progress
- Reduce admin time by 60%
- Improve member retention by 20%
- Professional image untuk attract new members

### 2. Core MVP Features

#### 2.1 Member Management
**MVP-F001: Member Registration**
```
User Story: Sebagai front desk, saya ingin mendaftarkan member baru dalam 3 menit
Acceptance Criteria:
- Form input: Nama, No HP, <PERSON><PERSON> (optional), <PERSON><PERSON><PERSON> (optional)
- Upload foto profil (optional)
- Auto-generate member ID (format: GYM001, GYM002, dst)
- Set membership package dan expiry date
- Save to Firestore dengan timestamp
- Email/SMS welcome message (optional)
```

**MVP-F002: Member Directory**
```
User Story: Sebagai staff, saya ingin mencari member dengan cepat
Acceptance Criteria:  
- Real-time search by nama atau phone number
- Filter by status (Active, Expired, Frozen)
- Sort by join date, expiry date, last visit
- View member card dengan QR code
- Quick actions: Edit, Extend membership, Check-in
```

**MVP-F003: Member Profile**
```
User Story: Sebagai member, saya ingin melihat profile saya
Acceptance Criteria:
- Display foto, info personal, membership status
- Membership expiry countdown
- Visit history (last 10 visits)
- Payment history
- QR code untuk check-in
```

#### 2.2 Membership Packages & Billing

**MVP-F004: Package Management**
```
Packages (Static untuk MVP):
1. Daily Pass - Rp 50,000 (1 hari)
2. Weekly Pass - Rp 200,000 (7 hari)  
3. Monthly Basic - Rp 400,000 (30 hari)
4. Monthly Unlimited - Rp 600,000 (30 hari + group class)
5. Quarterly - Rp 1,500,000 (90 hari + benefits)

Features:
- Package comparison table
- Auto-calculate expiry date
- Prorate pricing untuk partial months
```

**MVP-F005: Payment Tracking**
```
User Story: Sebagai owner, saya ingin track semua payment accurately
Acceptance Criteria:
- Manual payment entry oleh staff
- Payment methods: Cash, Transfer, QRIS, E-wallet
- Upload payment proof (photo)
- Auto-update member expiry date
- Generate digital receipt
- WhatsApp receipt sharing (optional)
```

#### 2.3 Check-in System

**MVP-F006: Digital Check-in**
```
User Story: Sebagai member, saya ingin check-in dengan mudah
Acceptance Criteria:
- QR code scanning (staff scan member QR)
- Manual search dan click check-in
- Record timestamp dan location
- Block check-in untuk expired members
- Daily visitor counter
- Peak hours analytics
```

**MVP-F007: Capacity Management**
```
User Story: Sebagai owner, saya ingin control capacity studio
Acceptance Criteria:
- Set max capacity (default: 30 orang)
- Real-time occupancy counter
- Alert when capacity 80% full
- Historical occupancy data
- Peak hours identification
```

#### 2.4 Staff Dashboard

**MVP-F008: Daily Operations Dashboard**
```
User Story: Sebagai staff, saya ingin overview operasional harian
Widgets:
- Today's visitors count
- Today's revenue
- Members expiring this week
- Current occupancy
- Quick check-in button
- Recent activities feed
```

**MVP-F009: Owner Analytics Dashboard**  
```
User Story: Sebagai owner, saya ingin insights bisnis
Widgets:
- Monthly revenue trend
- Member growth chart
- Retention rate
- Top payment methods
- Daily visitor pattern
- Revenue per member
```

#### 2.5 Basic Admin Functions

**MVP-F010: User Management**
```
User Roles:
1. Owner: Full access
2. Staff: Member management, check-in, payments
3. Member: View own profile only

Authentication: Firebase Auth
- Email/password login
- Password reset
- Role-based access control
```

### 3. Technical Architecture

#### 3.1 Frontend Stack
```
Technology: React.js (Web Application)
UI Framework: Tailwind CSS
State Management: React Context + useReducer
Deployment: Vercel/Netlify
PWA: Service worker untuk offline basic functionality
```

#### 3.2 Backend Architecture (Firebase)
```
Authentication: Firebase Authentication
Database: Cloud Firestore
Analytics: Firebase Analytics
```

#### 3.3 Firestore Database Structure
```javascript
// Collections Structure

// Users Collection
users: {
  userId: {
    email: string,
    role: 'owner' | 'staff' | 'member',
    displayName: string,
    photoURL: string,
    createdAt: timestamp,
    lastLogin: timestamp
  }
}

// Members Collection  
members: {
  memberId: {
    personalInfo: {
      name: string,
      phone: string,
      email: string,
      address: string,
      photoURL: string,
      emergencyContact: string
    },
    membership: {
      type: string, // 'daily', 'weekly', 'monthly-basic', etc
      startDate: timestamp,
      endDate: timestamp, 
      status: 'active' | 'expired' | 'frozen',
      autoRenew: boolean
    },
    stats: {
      totalVisits: number,
      lastVisit: timestamp,
      totalPaid: number,
      joinDate: timestamp
    },
    qrCode: string, // generated QR code string
    createdAt: timestamp,
    updatedAt: timestamp
  }
}

// Payments Collection
payments: {
  paymentId: {
    memberId: string,
    amount: number,
    method: string, // 'cash', 'transfer', 'qris', 'ewallet'
    packageType: string,
    duration: number, // days
    proofURL: string, // photo upload
    processedBy: userId,
    paymentDate: timestamp,
    receiptNumber: string,
    notes: string
  }
}

// Visits Collection
visits: {
  visitId: {
    memberId: string,
    memberName: string,
    checkInTime: timestamp,
    processedBy: userId,
    visitType: 'gym' | 'class',
    notes: string
  }
}

// Settings Collection (Single Document)
settings: {
  studio: {
    name: string,
    address: string,
    phone: string,
    email: string,
    maxCapacity: number,
    operatingHours: {
      open: string, // "06:00"
      close: string  // "22:00"
    },
    packages: {
      daily: { price: number, duration: number },
      weekly: { price: number, duration: number },
      monthly: { price: number, duration: number }
    }
  }
}
```

#### 3.4 Security Rules (Firestore)
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Users can only read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Members: Staff and Owner can CRUD, Members can only read own data
    match /members/{memberId} {
      allow read, write: if request.auth != null && 
        (getUserRole() in ['owner', 'staff'] || 
         resource.data.userId == request.auth.uid);
    }
    
    // Payments: Only staff and owner can access
    match /payments/{paymentId} {
      allow read, write: if request.auth != null && 
        getUserRole() in ['owner', 'staff'];
    }
    
    // Visits: Only staff and owner can write, members can read own visits
    match /visits/{visitId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        getUserRole() in ['owner', 'staff'];
    }
    
    // Settings: Only owner can modify
    match /settings/studio {
      allow read: if request.auth != null;
      allow write: if request.auth != null && getUserRole() == 'owner';
    }
    
    function getUserRole() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role;
    }
  }
}
```

### 4. User Experience Design

#### 4.1 Navigation Structure
```
Main Navigation (Sidebar):
├── Dashboard (📊)
├── Members (👥)
│   ├── All Members
│   ├── Add New Member
│   └── Expired Members
├── Check-in (📱)
├── Payments (💰)
├── Reports (📈)
└── Settings (⚙️)

Quick Actions (Top Bar):
- Search Members
- Quick Check-in
- Add Payment
- Current Occupancy Badge
```

#### 4.2 Key User Flows

**Flow 1: Member Registration (3 minutes)**
```
1. Click "Add New Member"
2. Fill form (Name*, Phone*, Package*)
3. Upload photo (optional)
4. Select package & calculate expiry
5. Save → Generate QR code
6. Print welcome card/send WhatsApp
```

**Flow 2: Daily Check-in (30 seconds)**
```
1. Member shows QR code
2. Staff scan OR search member name
3. Click "Check In"
4. Success animation + current occupancy update
```

**Flow 3: Payment Processing (2 minutes)**
```
1. Select member from dropdown
2. Choose package type
3. Enter amount & payment method
4. Upload payment proof (optional)
5. Confirm → Update member expiry
6. Generate receipt
```

#### 4.3 Mobile Responsive Priority
```
Primary Device: Tablet (iPad/Android tablet di front desk)
Secondary: Desktop (untuk owner/admin)
Tertiary: Mobile phone (untuk quick check-in)

Key Mobile Optimizations:
- Large touch targets (min 48px)
- One-handed operation
- Offline basic functionality
- Fast loading (<3 seconds on 4G)
```

### 5. Development Phases

#### Phase 1: Foundation (Week 1-4)
```
Deliverables:
✅ Firebase project setup
✅ Authentication system (login/logout)
✅ Basic UI framework dengan Tailwind
✅ Members CRUD (Create, Read, Update, Delete)
✅ Responsive design foundation
✅ Firebase security rules basic
```

#### Phase 2: Core Features (Week 5-8) 
```
Deliverables:
✅ Check-in system dengan QR code
✅ Payment tracking dan receipts
✅ Dashboard dengan basic analytics
✅ Search dan filter members
✅ Membership expiry tracking
✅ Data validation dan error handling
```

#### Phase 3: Polish & Testing (Week 9-12)
```
Deliverables:
✅ UI/UX improvements
✅ Performance optimization
✅ User acceptance testing
✅ Data backup procedures
✅ Training materials
✅ Go-live preparation
```

### 6. Firebase Costs Estimation

#### 6.1 Firestore Usage (Monthly)
```
Assumptions:
- 100 active members
- 500 check-ins per month
- 100 payments per month
- 20 new members per month

Document Reads: ~10,000/month
Document Writes: ~2,000/month
Storage: <1GB

Cost: $1-5 per month (very low for MVP scale)
```

#### 6.2 Firebase Storage
```
Usage: Member photos, payment proofs
Estimate: 2GB storage, 10GB bandwidth
Cost: $1-2 per month
```

#### 6.3 Firebase Hosting
```
Usage: Web app hosting
Cost: Free tier sufficient untuk MVP
```

**Total Firebase Cost: $5-10/month (sangat affordable!)**

### 7. Development Budget

#### 7.1 Development Cost
```
Lead Developer (Full-stack): Rp 80-120 juta (3 bulan)
UI/UX Designer: Rp 20-30 juta
Testing & QA: Rp 10-20 juta
Project Management: Rp 10-15 juta

Total Development: Rp 120-185 juta
```

#### 7.2 Operational Cost (Year 1)
```
Firebase: $120/year = Rp 1.8 juta
Domain (.co.id): Rp 200 ribu
Support & Maintenance: Rp 20-40 juta

Total Operational: Rp 22-42 juta/year
```

**Total MVP Investment: Rp 145-225 juta**

### 8. Success Metrics & KPIs

#### 8.1 User Adoption
- Staff menggunakan sistem untuk 90% transaksi dalam 2 minggu
- Member check-in menggunakan QR code 70%+ dalam 1 bulan  
- Owner mengakses dashboard minimal 3x per minggu

#### 8.2 Business Impact
- Reduce admin time dari 4 jam/hari ke 2 jam/hari
- Member retention meningkat 20% (dari 60% ke 72%)
- Revenue visibility 100% (no missing payment records)
- Member satisfaction rating 4.5/5

#### 8.3 Technical Performance
- Page load time <3 seconds
- System uptime 99.5%
- Data sync delay <1 second
- Zero data loss incidents

### 9. Risk Mitigation

#### 9.1 Technical Risks
**Risk**: Internet dependency
**Mitigation**: Progressive Web App dengan offline capability untuk basic functions

**Risk**: Firebase cost escalation  
**Mitigation**: Monitor usage dashboard, set billing alerts

**Risk**: Data loss
**Mitigation**: Firebase automatic backup + weekly manual exports

#### 9.2 Business Risks  
**Risk**: Staff resistance to change
**Mitigation**: Intensive training, gradual rollout, highlight time-saving benefits

**Risk**: Low member adoption
**Mitigation**: Incentives untuk QR check-in, member education program

### 10. Post-MVP Roadmap

#### Version 2.0 (Month 4-6)
- Member mobile app
- WhatsApp integration untuk notifications  
- Online payment gateway (Midtrans)
- Class scheduling system
- Automated membership renewal reminders

#### Version 3.0 (Month 7-9)
- Personal trainer booking
- Workout tracking dan progress
- Member community features
- Advanced analytics dengan AI insights
- Multi-location support

#### Long-term Vision
- IoT integration (smart access control)
- Wearable device integration
- Franchise management system
- Marketplace untuk supplements

### 11. Implementation Checklist

#### Pre-Development
- [ ] Finalize UI/UX mockups
- [ ] Setup Firebase project
- [ ] Configure development environment
- [ ] Create project roadmap
- [ ] Define testing procedures

#### Development Phase
- [ ] Setup CI/CD pipeline
- [ ] Implement authentication
- [ ] Build member management
- [ ] Create check-in system
- [ ] Add payment tracking
- [ ] Build dashboards
- [ ] Implement security rules
- [ ] Performance optimization

#### Pre-Launch
- [ ] User acceptance testing
- [ ] Staff training sessions
- [ ] Data migration (if needed)
- [ ] Performance testing
- [ ] Security audit
- [ ] Backup procedures
- [ ] Go-live support plan

#### Post-Launch
- [ ] Monitor system performance
- [ ] Collect user feedback
- [ ] Address bugs quickly
- [ ] Plan next iteration
- [ ] Scale infrastructure if needed

---

## Conclusion

MVP ini dirancang khusus untuk studio gym skala awal yang ingin bertransisi dari sistem manual ke digital dengan budget terbatas tapi teknologi modern. Dengan Firebase/Firestore sebagai backend, owner dapat fokus pada fitur bisnis tanpa khawatir tentang infrastructure management.

**Key Advantages:**
- **Low Total Cost of Ownership**: Firebase eliminates server management costs
- **Scalable**: Can grow from 50 to 1000+ members seamlessly  
- **Real-time**: All data updates instantly across devices
- **Reliable**: Google's infrastructure ensures 99.9% uptime
- **Secure**: Built-in security features dan GDPR compliant

**Next Step**: Mulai dengan mockup design dan Firebase project setup untuk validate technical approach sebelum full development.