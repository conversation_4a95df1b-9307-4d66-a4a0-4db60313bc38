'use client'

import { useState, useMemo } from 'react'
import { Search, Filter, Download, Eye, CreditCard } from 'lucide-react'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select } from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/hooks/useAuth'
import { mockPayments } from '@/data/mockData'
import { formatCurrency, formatDateTime, formatDate } from '@/lib/utils'

export default function PaymentHistoryPage() {
  const { user } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [methodFilter, setMethodFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')
  const [sortBy, setSortBy] = useState('date')

  // Filter and sort payments
  const filteredPayments = useMemo(() => {
    let filtered = mockPayments.filter(payment => {
      // Search filter
      const matchesSearch = searchQuery === '' || 
        payment.memberName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        payment.memberId.toLowerCase().includes(searchQuery.toLowerCase()) ||
        payment.receiptNumber.toLowerCase().includes(searchQuery.toLowerCase())

      // Method filter
      const matchesMethod = methodFilter === 'all' || payment.method === methodFilter

      // Date filter
      let matchesDate = true
      if (dateFilter !== 'all') {
        const now = new Date()
        const paymentDate = payment.paymentDate
        
        switch (dateFilter) {
          case 'today':
            matchesDate = paymentDate.toDateString() === now.toDateString()
            break
          case 'week':
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
            matchesDate = paymentDate >= weekAgo
            break
          case 'month':
            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
            matchesDate = paymentDate >= monthAgo
            break
        }
      }

      return matchesSearch && matchesMethod && matchesDate
    })

    // Sort payments
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'amount':
          return b.amount - a.amount
        case 'member':
          return a.memberName.localeCompare(b.memberName)
        case 'method':
          return a.method.localeCompare(b.method)
        case 'date':
        default:
          return b.paymentDate.getTime() - a.paymentDate.getTime()
      }
    })

    return filtered
  }, [searchQuery, methodFilter, dateFilter, sortBy])

  const getPaymentMethodDisplay = (method: string) => {
    switch (method) {
      case 'cash':
        return { icon: '💰', label: 'Cash', variant: 'default' as const }
      case 'transfer':
        return { icon: '🏦', label: 'Transfer', variant: 'secondary' as const }
      case 'qris':
        return { icon: '📱', label: 'QRIS', variant: 'default' as const }
      case 'ewallet':
        return { icon: '💳', label: 'E-wallet', variant: 'secondary' as const }
      default:
        return { icon: '💰', label: method, variant: 'default' as const }
    }
  }

  const getPackageDisplayName = (packageType: string) => {
    switch (packageType) {
      case 'daily':
        return 'Daily Pass'
      case 'weekly':
        return 'Weekly Pass'
      case 'monthly-basic':
        return 'Monthly Basic'
      case 'monthly-unlimited':
        return 'Monthly Unlimited'
      case 'quarterly':
        return 'Quarterly'
      default:
        return packageType
    }
  }

  const getTotalRevenue = () => {
    return filteredPayments.reduce((sum, payment) => sum + payment.amount, 0)
  }

  const handleViewReceipt = (payment: any) => {
    console.log('View receipt for:', payment)
    // TODO: Open receipt modal or navigate to receipt page
  }

  const handleExport = () => {
    console.log('Export payments:', filteredPayments)
    // TODO: Export to CSV or PDF
    alert('Export functionality would be implemented here')
  }

  return (
    <ProtectedRoute>
      <MainLayout user={user}>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">💰 Payment History</h1>
              <p className="text-muted-foreground">
                {filteredPayments.length} payments • Total: {formatCurrency(getTotalRevenue())}
              </p>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button>
                <CreditCard className="h-4 w-4 mr-2" />
                New Payment
              </Button>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search by member, receipt, or ID..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filters */}
            <div className="flex items-center space-x-2">
              <Select
                value={methodFilter}
                onChange={(e) => setMethodFilter(e.target.value)}
              >
                <option value="all">All Methods</option>
                <option value="cash">Cash</option>
                <option value="transfer">Transfer</option>
                <option value="qris">QRIS</option>
                <option value="ewallet">E-wallet</option>
              </Select>

              <Select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
              >
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
              </Select>

              <Select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="date">Sort by: Date</option>
                <option value="amount">Sort by: Amount</option>
                <option value="member">Sort by: Member</option>
                <option value="method">Sort by: Method</option>
              </Select>
            </div>
          </div>

          {/* Payments List */}
          <div className="space-y-4">
            {filteredPayments.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-muted-foreground">No payments found matching your criteria.</p>
                <Button className="mt-4">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Record First Payment
                </Button>
              </div>
            ) : (
              filteredPayments.map((payment) => {
                const methodInfo = getPaymentMethodDisplay(payment.method)
                
                return (
                  <Card key={payment.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        {/* Payment Info */}
                        <div className="flex items-start space-x-4 flex-1">
                          <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                            <span className="text-lg">{methodInfo.icon}</span>
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="font-semibold">{payment.memberName}</span>
                              <span className="text-sm text-muted-foreground">
                                ({payment.memberId})
                              </span>
                              <Badge variant={methodInfo.variant}>
                                {methodInfo.label}
                              </Badge>
                            </div>
                            
                            <div className="text-sm text-muted-foreground space-y-1">
                              <div>
                                {getPackageDisplayName(payment.packageType)} • {payment.duration} days
                              </div>
                              <div className="flex items-center space-x-4">
                                <span>📅 {formatDateTime(payment.paymentDate)}</span>
                                <span>🧾 {payment.receiptNumber}</span>
                              </div>
                              {payment.notes && (
                                <div className="text-xs">
                                  📝 {payment.notes}
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Amount */}
                          <div className="text-right">
                            <div className="text-xl font-bold text-green-600">
                              {formatCurrency(payment.amount)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {formatDate(payment.paymentDate)}
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewReceipt(payment)}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            Receipt
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })
            )}
          </div>

          {/* Pagination */}
          {filteredPayments.length > 20 && (
            <div className="flex items-center justify-center space-x-2">
              <Button variant="outline" size="sm">
                ← Previous
              </Button>
              <span className="text-sm text-muted-foreground">
                Page 1 of {Math.ceil(filteredPayments.length / 20)}
              </span>
              <Button variant="outline" size="sm">
                Next →
              </Button>
            </div>
          )}
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
