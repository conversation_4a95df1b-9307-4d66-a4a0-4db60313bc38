import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

interface StatsCardProps {
  title: string
  value: string | number
  subtitle?: string
  trend?: {
    value: number
    label: string
    type: 'up' | 'down' | 'neutral'
  }
  status?: {
    label: string
    variant: 'success' | 'warning' | 'destructive' | 'default'
  }
  icon?: React.ReactNode
}

export function StatsCard({ 
  title, 
  value, 
  subtitle, 
  trend, 
  status, 
  icon 
}: StatsCardProps) {
  const formatValue = (val: string | number) => {
    if (typeof val === 'number' && val > 1000000) {
      return formatCurrency(val)
    }
    return val.toString()
  }

  const getTrendIcon = (type: 'up' | 'down' | 'neutral') => {
    switch (type) {
      case 'up':
        return <TrendingUp className="h-3 w-3" />
      case 'down':
        return <TrendingDown className="h-3 w-3" />
      case 'neutral':
        return <Minus className="h-3 w-3" />
    }
  }

  const getTrendColor = (type: 'up' | 'down' | 'neutral') => {
    switch (type) {
      case 'up':
        return 'text-green-600'
      case 'down':
        return 'text-red-600'
      case 'neutral':
        return 'text-gray-600'
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        {icon && <div className="text-muted-foreground">{icon}</div>}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formatValue(value)}</div>
        {subtitle && (
          <p className="text-xs text-muted-foreground mt-1">{subtitle}</p>
        )}
        
        <div className="flex items-center justify-between mt-2">
          {trend && (
            <div className={`flex items-center text-xs ${getTrendColor(trend.type)}`}>
              {getTrendIcon(trend.type)}
              <span className="ml-1">{trend.label}</span>
            </div>
          )}
          
          {status && (
            <Badge variant={status.variant} className="text-xs">
              {status.label}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
